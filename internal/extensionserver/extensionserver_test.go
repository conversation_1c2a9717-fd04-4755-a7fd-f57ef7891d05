// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extensionserver

import (
	"bytes"
	"context"
	"log/slog"
	"testing"
	"time"

	egextension "github.com/envoyproxy/gateway/proto/extension"
	clusterv3 "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	endpointv3 "github.com/envoyproxy/go-control-plane/envoy/config/endpoint/v3"
	routev3 "github.com/envoyproxy/go-control-plane/envoy/config/route/v3"
	"github.com/go-logr/logr"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/wrapperspb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	gwaiev1a2 "sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2"

	aigv1a1 "github.com/envoyproxy/ai-gateway/api/v1alpha1"
	"github.com/envoyproxy/ai-gateway/internal/controller"
	"github.com/envoyproxy/ai-gateway/internal/internalapi"
)

func newFakeClient() client.Client {
	builder := fake.NewClientBuilder().WithScheme(controller.Scheme).
		WithStatusSubresource(&aigv1a1.AIGatewayRoute{}).
		WithStatusSubresource(&aigv1a1.AIServiceBackend{}).
		WithStatusSubresource(&aigv1a1.BackendSecurityPolicy{})
	return builder.Build()
}

const udsPath = "/tmp/uds/test.sock"

func TestNew(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)
	require.NotNil(t, s)
}

func TestCheck(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)
	_, err := s.Check(t.Context(), nil)
	require.NoError(t, err)
}

func TestWatch(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)
	err := s.Watch(nil, nil)
	require.Error(t, err)
	require.Equal(t, "rpc error: code = Unimplemented desc = Watch is not implemented", err.Error())
}

func TestServerPostTranslateModify(t *testing.T) {
	t.Run("existing", func(t *testing.T) {
		s := New(newFakeClient(), logr.Discard(), udsPath)
		req := &egextension.PostTranslateModifyRequest{Clusters: []*clusterv3.Cluster{{Name: extProcUDSClusterName}}}
		res, err := s.PostTranslateModify(t.Context(), req)
		require.Equal(t, &egextension.PostTranslateModifyResponse{
			Clusters: req.Clusters, Secrets: req.Secrets,
		}, res)
		require.NoError(t, err)
	})
	t.Run("not existing", func(t *testing.T) {
		s := New(newFakeClient(), logr.Discard(), udsPath)
		res, err := s.PostTranslateModify(t.Context(), &egextension.PostTranslateModifyRequest{
			Clusters: []*clusterv3.Cluster{{Name: "foo"}},
		})
		require.NotNil(t, res)
		require.NoError(t, err)
		require.Len(t, res.Clusters, 2)
		require.Equal(t, "foo", res.Clusters[0].Name)
		require.Equal(t, extProcUDSClusterName, res.Clusters[1].Name)
	})
}

func Test_maybeModifyCluster(t *testing.T) {
	c := newFakeClient()

	// Create some fake AIGatewayRoute objects.
	err := c.Create(t.Context(), &aigv1a1.AIGatewayRoute{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "myroute",
			Namespace: "ns",
		},
		Spec: aigv1a1.AIGatewayRouteSpec{
			Rules: []aigv1a1.AIGatewayRouteRule{
				{
					BackendRefs: []aigv1a1.AIGatewayRouteRuleBackendRef{
						{Name: "aaa", Priority: ptr.To[uint32](0)},
						{Name: "bbb", Priority: ptr.To[uint32](1)},
					},
				},
			},
		},
	})
	require.NoError(t, err)

	for _, tc := range []struct {
		c      *clusterv3.Cluster
		errLog string
	}{
		{c: &clusterv3.Cluster{}, errLog: "non-ai-gateway cluster name"},
		{c: &clusterv3.Cluster{
			Name: "httproute/ns/name/rule/invalid",
		}, errLog: "failed to parse HTTPRoute rule index"},
		{c: &clusterv3.Cluster{
			Name: "httproute/ns/nonexistent/rule/0",
		}, errLog: `failed to get AIGatewayRoute object`},
		{c: &clusterv3.Cluster{
			Name: "httproute/ns/myroute/rule/99999",
		}, errLog: `HTTPRoute rule index out of range`},
		{c: &clusterv3.Cluster{
			Name: "httproute/ns/myroute/rule/0",
		}, errLog: `LoadAssignment is nil`},
		{c: &clusterv3.Cluster{
			Name:           "httproute/ns/myroute/rule/0",
			LoadAssignment: &endpointv3.ClusterLoadAssignment{},
		}, errLog: `LoadAssignment endpoints length does not match backend refs length`},
	} {
		t.Run("error/"+tc.errLog, func(t *testing.T) {
			var buf bytes.Buffer
			s := New(c, logr.FromSlogHandler(slog.NewTextHandler(&buf, &slog.HandlerOptions{})), udsPath)
			s.maybeModifyCluster(tc.c)
			t.Logf("buf: %s", buf.String())
			require.Contains(t, buf.String(), tc.errLog)
		})
	}
	t.Run("ok", func(t *testing.T) {
		cluster := &clusterv3.Cluster{
			Name: "httproute/ns/myroute/rule/0",
			LoadAssignment: &endpointv3.ClusterLoadAssignment{
				Endpoints: []*endpointv3.LocalityLbEndpoints{
					{
						LbEndpoints: []*endpointv3.LbEndpoint{
							{},
						},
					},
					{
						LbEndpoints: []*endpointv3.LbEndpoint{
							{},
						},
					},
				},
			},
		}
		var buf bytes.Buffer
		s := New(c, logr.FromSlogHandler(slog.NewTextHandler(&buf, &slog.HandlerOptions{})), udsPath)
		s.maybeModifyCluster(cluster)
		require.Empty(t, buf.String())

		require.Len(t, cluster.LoadAssignment.Endpoints, 2)
		require.Len(t, cluster.LoadAssignment.Endpoints[0].LbEndpoints, 1)
		require.Equal(t, uint32(0), cluster.LoadAssignment.Endpoints[0].Priority)
		require.Equal(t, uint32(1), cluster.LoadAssignment.Endpoints[1].Priority)
		md := cluster.LoadAssignment.Endpoints[0].LbEndpoints[0].Metadata
		require.NotNil(t, md)
		require.Len(t, md.FilterMetadata, 1)
		mmd, ok := md.FilterMetadata[internalapi.InternalEndpointMetadataNamespace]
		require.True(t, ok)
		require.Len(t, mmd.Fields, 1)
		require.Equal(t, "ns/aaa/route/myroute/rule/0/ref/0", mmd.Fields[internalapi.InternalMetadataBackendNameKey].GetStringValue())
	})
}

// Helper function to create an InferencePool ExtensionResource
func createInferencePoolExtensionResource(name, namespace string) *egextension.ExtensionResource {
	unstructuredObj := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "inference.networking.x-k8s.io/v1alpha2",
			"kind":       "InferencePool",
			"metadata": map[string]interface{}{
				"name":      name,
				"namespace": namespace,
			},
			"spec": map[string]interface{}{
				"targetPortNumber": int32(8080),
				"selector": map[string]interface{}{
					"app": "test-inference",
				},
				"extensionRef": map[string]interface{}{
					"name": "test-epp",
				},
			},
		},
	}

	// Marshal to JSON bytes
	jsonBytes, _ := unstructuredObj.MarshalJSON()
	return &egextension.ExtensionResource{
		UnstructuredBytes: jsonBytes,
	}
}

// TestPostClusterModify tests the PostClusterModify method
func TestPostClusterModify(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)

	t.Run("nil cluster", func(t *testing.T) {
		req := &egextension.PostClusterModifyRequest{Cluster: nil}
		resp, err := s.PostClusterModify(context.Background(), req)
		require.NoError(t, err)
		require.Nil(t, resp)
	})

	t.Run("no backend extension resources", func(t *testing.T) {
		cluster := &clusterv3.Cluster{Name: "test-cluster"}
		req := &egextension.PostClusterModifyRequest{
			Cluster: cluster,
			PostClusterContext: &egextension.PostClusterExtensionContext{
				BackendExtensionResources: []*egextension.ExtensionResource{},
			},
		}
		resp, err := s.PostClusterModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, cluster, resp.Cluster)
	})

	t.Run("nil PostClusterContext", func(t *testing.T) {
		cluster := &clusterv3.Cluster{Name: "test-cluster"}
		req := &egextension.PostClusterModifyRequest{
			Cluster:            cluster,
			PostClusterContext: nil,
		}
		resp, err := s.PostClusterModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, cluster, resp.Cluster)
	})

	t.Run("with InferencePool backend", func(t *testing.T) {
		// Use a logger that captures output for debugging
		var buf bytes.Buffer
		logger := logr.FromSlogHandler(slog.NewTextHandler(&buf, &slog.HandlerOptions{}))
		s := New(newFakeClient(), logger, udsPath)

		cluster := &clusterv3.Cluster{
			Name:     "test-cluster",
			LbPolicy: clusterv3.Cluster_ROUND_ROBIN,
		}
		inferencePool := createInferencePoolExtensionResource("test-pool", "default")

		// Debug: print the JSON to see what we're sending
		t.Logf("InferencePool JSON: %s", string(inferencePool.UnstructuredBytes))

		req := &egextension.PostClusterModifyRequest{
			Cluster: cluster,
			PostClusterContext: &egextension.PostClusterExtensionContext{
				BackendExtensionResources: []*egextension.ExtensionResource{inferencePool},
			},
		}
		resp, err := s.PostClusterModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, cluster, resp.Cluster)

		// Print logs for debugging
		t.Logf("Server logs: %s", buf.String())

		// Verify cluster was modified for ORIGINAL_DST
		if cluster.ClusterDiscoveryType == nil {
			t.Logf("ClusterDiscoveryType is nil - cluster was not modified")
			t.Logf("Cluster LbPolicy: %v", cluster.LbPolicy)
			t.FailNow()
		}
		require.NotNil(t, cluster.ClusterDiscoveryType)
		require.Equal(t, clusterv3.Cluster_ORIGINAL_DST, cluster.ClusterDiscoveryType.(*clusterv3.Cluster_Type).Type)
		require.Equal(t, clusterv3.Cluster_CLUSTER_PROVIDED, cluster.LbPolicy)
		require.Equal(t, durationpb.New(1000*time.Second), cluster.ConnectTimeout)
		require.NotNil(t, cluster.LbConfig)
		require.Nil(t, cluster.LoadBalancingPolicy)
		require.Nil(t, cluster.EdsClusterConfig)
	})
}

// TestPostRouteModify tests the PostRouteModify method
func TestPostRouteModify(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)

	t.Run("nil route", func(t *testing.T) {
		req := &egextension.PostRouteModifyRequest{Route: nil}
		resp, err := s.PostRouteModify(context.Background(), req)
		require.NoError(t, err)
		require.Nil(t, resp)
	})

	t.Run("no extension resources", func(t *testing.T) {
		route := &routev3.Route{Name: "test-route"}
		req := &egextension.PostRouteModifyRequest{
			Route: route,
			PostRouteContext: &egextension.PostRouteExtensionContext{
				ExtensionResources: []*egextension.ExtensionResource{},
			},
		}
		resp, err := s.PostRouteModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, route, resp.Route)
	})

	t.Run("nil PostRouteContext", func(t *testing.T) {
		route := &routev3.Route{Name: "test-route"}
		req := &egextension.PostRouteModifyRequest{
			Route:            route,
			PostRouteContext: nil,
		}
		resp, err := s.PostRouteModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, route, resp.Route)
	})

	t.Run("with InferencePool extension", func(t *testing.T) {
		route := &routev3.Route{
			Name: "test-route",
			Action: &routev3.Route_Route{
				Route: &routev3.RouteAction{},
			},
		}
		inferencePool := createInferencePoolExtensionResource("test-pool", "default")
		req := &egextension.PostRouteModifyRequest{
			Route: route,
			PostRouteContext: &egextension.PostRouteExtensionContext{
				ExtensionResources: []*egextension.ExtensionResource{inferencePool},
			},
		}
		resp, err := s.PostRouteModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, route, resp.Route)

		// Verify route was modified
		require.Equal(t, wrapperspb.Bool(false), route.GetRoute().GetAutoHostRewrite())
		require.NotNil(t, route.TypedPerFilterConfig)
	})
}

// TestConstructInferencePoolsFrom tests the constructInferencePoolsFrom method
func TestConstructInferencePoolsFrom(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)

	t.Run("empty resources", func(t *testing.T) {
		result := s.constructInferencePoolsFrom([]*egextension.ExtensionResource{})
		require.Empty(t, result)
	})

	t.Run("valid InferencePool", func(t *testing.T) {
		inferencePool := createInferencePoolExtensionResource("test-pool", "default")
		result := s.constructInferencePoolsFrom([]*egextension.ExtensionResource{inferencePool})
		require.Len(t, result, 1)
		require.Equal(t, "test-pool", result[0].Name)
		require.Equal(t, "default", result[0].Namespace)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		invalidResource := &egextension.ExtensionResource{
			UnstructuredBytes: []byte("invalid json"),
		}
		result := s.constructInferencePoolsFrom([]*egextension.ExtensionResource{invalidResource})
		require.Empty(t, result)
	})

	t.Run("wrong API version", func(t *testing.T) {
		unstructuredObj := &unstructured.Unstructured{
			Object: map[string]interface{}{
				"apiVersion": "v1",
				"kind":       "Service",
				"metadata": map[string]interface{}{
					"name":      "test-service",
					"namespace": "default",
				},
			},
		}
		jsonBytes, _ := unstructuredObj.MarshalJSON()
		wrongResource := &egextension.ExtensionResource{
			UnstructuredBytes: jsonBytes,
		}
		result := s.constructInferencePoolsFrom([]*egextension.ExtensionResource{wrongResource})
		require.Empty(t, result)
	})
}

// TestInferencePoolHelperFunctions tests various helper functions for InferencePool
func TestInferencePoolHelperFunctions(t *testing.T) {
	// Create a test InferencePool
	pool := &gwaiev1a2.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pool",
			Namespace: "test-ns",
		},
		Spec: gwaiev1a2.InferencePoolSpec{
			TargetPortNumber: 8080,
			EndpointPickerConfig: gwaiev1a2.EndpointPickerConfig{
				ExtensionRef: &gwaiev1a2.Extension{
					ExtensionReference: gwaiev1a2.ExtensionReference{
						Name: "test-epp",
					},
				},
			},
		},
	}

	t.Run("authorityForInferencePool", func(t *testing.T) {
		authority := authorityForInferencePool(pool)
		require.Equal(t, "test-epp.test-ns.svc:9002", authority)
	})

	t.Run("dnsNameForInferencePool", func(t *testing.T) {
		dnsName := dnsNameForInferencePool(pool)
		require.Equal(t, "test-epp.test-ns.svc", dnsName)
	})

	t.Run("clusterNameForInferencePool", func(t *testing.T) {
		clusterName := clusterNameForInferencePool(pool)
		require.Equal(t, "envoy.clusters.endpointpicker_test-pool_test-ns_ext_proc", clusterName)
	})

	t.Run("httpFilterNameForInferencePool", func(t *testing.T) {
		filterName := httpFilterNameForInferencePool(pool)
		require.Equal(t, "envoy.filters.http.endpointpicker_test-pool_test-ns_ext_proc", filterName)
	})

	t.Run("portForInferencePool default", func(t *testing.T) {
		port := portForInferencePool(pool)
		require.Equal(t, uint32(9002), port) // default port
	})

	t.Run("portForInferencePool custom", func(t *testing.T) {
		customPool := pool.DeepCopy()
		customPort := gwaiev1a2.PortNumber(8888)
		customPool.Spec.ExtensionRef.ExtensionReference.PortNumber = &customPort
		port := portForInferencePool(customPool)
		require.Equal(t, uint32(8888), port)
	})
}

// TestBuildExtProcClusterForInferencePoolEndpointPicker tests cluster building
func TestBuildExtProcClusterForInferencePoolEndpointPicker(t *testing.T) {
	pool := &gwaiev1a2.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pool",
			Namespace: "test-ns",
		},
		Spec: gwaiev1a2.InferencePoolSpec{
			TargetPortNumber: 8080,
			EndpointPickerConfig: gwaiev1a2.EndpointPickerConfig{
				ExtensionRef: &gwaiev1a2.Extension{
					ExtensionReference: gwaiev1a2.ExtensionReference{
						Name: "test-epp",
					},
				},
			},
		},
	}

	t.Run("valid pool", func(t *testing.T) {
		cluster := buildExtProcClusterForInferencePoolEndpointPicker(pool)
		require.NotNil(t, cluster)
		require.Equal(t, "envoy.clusters.endpointpicker_test-pool_test-ns_ext_proc", cluster.Name)
		require.Equal(t, clusterv3.Cluster_STRICT_DNS, cluster.GetType())
		require.Equal(t, clusterv3.Cluster_LEAST_REQUEST, cluster.LbPolicy)
		require.NotNil(t, cluster.LoadAssignment)
		require.Len(t, cluster.LoadAssignment.Endpoints, 1)
	})

	t.Run("nil pool panics", func(t *testing.T) {
		require.Panics(t, func() {
			buildExtProcClusterForInferencePoolEndpointPicker(nil)
		})
	})

	t.Run("nil ExtensionRef panics", func(t *testing.T) {
		invalidPool := pool.DeepCopy()
		invalidPool.Spec.EndpointPickerConfig.ExtensionRef = nil
		require.Panics(t, func() {
			buildExtProcClusterForInferencePoolEndpointPicker(invalidPool)
		})
	})
}

// TestBuildClustersForInferencePoolEndpointPickers tests building clusters from existing clusters
func TestBuildClustersForInferencePoolEndpointPickers(t *testing.T) {
	// Create a cluster with InferencePool metadata
	cluster := &clusterv3.Cluster{
		Name: "test-cluster",
		Metadata: &corev3.Metadata{
			FilterMetadata: map[string]*structpb.Struct{
				internalapi.InternalEndpointMetadataNamespace: {
					Fields: map[string]*structpb.Value{
						"per_route_rule_inference_pool": structpb.NewStringValue("test-ns/test-pool/test-epp/9002"),
					},
				},
			},
		},
	}

	t.Run("with InferencePool metadata", func(t *testing.T) {
		clusters := []*clusterv3.Cluster{cluster}
		result := buildClustersForInferencePoolEndpointPickers(clusters)
		require.Len(t, result, 1)
		require.Contains(t, result[0].Name, "endpointpicker")
	})

	t.Run("without InferencePool metadata", func(t *testing.T) {
		normalCluster := &clusterv3.Cluster{Name: "normal-cluster"}
		clusters := []*clusterv3.Cluster{normalCluster}
		result := buildClustersForInferencePoolEndpointPickers(clusters)
		require.Empty(t, result)
	})
}

// TestMustToAny tests the mustToAny helper function
func TestMustToAny(t *testing.T) {
	t.Run("valid message", func(t *testing.T) {
		cluster := &clusterv3.Cluster{Name: "test"}
		any := mustToAny(cluster)
		require.NotNil(t, any)
		require.Contains(t, any.TypeUrl, "envoy.config.cluster.v3.Cluster")
	})
}

// TestPostTranslateModify tests the PostTranslateModify method
func TestPostTranslateModify(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)

	t.Run("empty request", func(t *testing.T) {
		req := &egextension.PostTranslateModifyRequest{}
		resp, err := s.PostTranslateModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
	})

	t.Run("with clusters", func(t *testing.T) {
		cluster := &clusterv3.Cluster{Name: "test-cluster"}
		req := &egextension.PostTranslateModifyRequest{
			Clusters: []*clusterv3.Cluster{cluster},
		}
		resp, err := s.PostTranslateModify(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		// Should have original cluster plus the UDS cluster
		require.Len(t, resp.Clusters, 2)
		require.Equal(t, "test-cluster", resp.Clusters[0].Name)
		require.Equal(t, "ai-gateway-extproc-uds", resp.Clusters[1].Name)
	})
}

// TestList tests the List method (health check)
func TestList(t *testing.T) {
	logger := logr.Discard()
	s := New(newFakeClient(), logger, udsPath)

	t.Run("list health statuses", func(t *testing.T) {
		resp, err := s.List(context.Background(), &grpc_health_v1.HealthListRequest{})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotEmpty(t, resp.Statuses)
		require.Contains(t, resp.Statuses, "envoy-gateway-extension-server")
	})
}
